{"name": "@types/p-limit", "version": "2.1.0", "description": "TypeScript definitions for p-limit", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU", "githubUsername": "LinusU"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/p-limit"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "7f7de58ce23a82db39b726ad88c46b3da5ba59da26447acac51d9b8258df9a01", "typeScriptVersion": "3.0"}