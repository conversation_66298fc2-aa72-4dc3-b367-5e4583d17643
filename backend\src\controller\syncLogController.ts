// controllers/syncLogController.ts

import { Request, Response } from 'express';
import { sendSuccess, sendError } from '../utils/responseHandler';
import syncLogService from '../service/syncLogService';

// Helper function to determine appropriate HTTP status code
const getStatusCode = (error: Error): number => {
    if (
        error.message.includes('required') ||
        error.message.includes('must be') ||
        error.message.includes('Invalid')
    ) {
        return 400; // Bad Request
    }
    if (
        error.message.includes('not found') ||
        (error.message.includes('No') && error.message.includes('found'))
    ) {
        return 404; // Not Found
    }
    if (
        error.message.includes('Authentication') ||
        error.message.includes('expired') ||
        error.message.includes('token')
    ) {
        return 401; // Unauthorized
    }
    if (
        error.message.includes('forbidden') ||
        error.message.includes('permissions')
    ) {
        return 403; // Forbidden
    }
    return 500; // Internal Server Error
};

/**
 * Get all sync logs with pagination and filtering
 * GET /api/v1/qbo/sync-logs
 */
const getSyncLogs = async (req: Request, res: Response): Promise<Response> => {
    try {
        const { 
            page = '1', 
            limit = '50', 
            transactionType,
            status,
            operation,
            dateFrom,
            dateTo,
            systemTransactionId,
            quickbooksId
        } = req.query as {
            page?: string;
            limit?: string;
            transactionType?: string;
            status?: string;
            operation?: string;
            dateFrom?: string;
            dateTo?: string;
            systemTransactionId?: string;
            quickbooksId?: string;
        };

        // Parse and validate query parameters
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);

        if (isNaN(pageNum) || pageNum < 1) {
            return sendError(res, 'Page must be a positive number', null, 400);
        }

        if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
            return sendError(res, 'Limit must be between 1 and 100', null, 400);
        }

        // Validate transaction type if provided
        if (transactionType) {
            const validTypes = ['INVOICE', 'PAYMENT', 'CUSTOMER', 'ITEM', 'ACCOUNT', 'CHART_OF_ACCOUNT'];
            if (!validTypes.includes(transactionType)) {
                return sendError(res, `Transaction type must be one of: ${validTypes.join(', ')}`, null, 400);
            }
        }

        // Validate status if provided
        if (status) {
            const validStatuses = ['PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'RETRY', 'CANCELLED'];
            if (!validStatuses.includes(status)) {
                return sendError(res, `Status must be one of: ${validStatuses.join(', ')}`, null, 400);
            }
        }

        // Validate operation if provided
        if (operation) {
            const validOperations = ['CREATE', 'UPDATE', 'DELETE', 'READ'];
            if (!validOperations.includes(operation)) {
                return sendError(res, `Operation must be one of: ${validOperations.join(', ')}`, null, 400);
            }
        }

        // Validate dates if provided
        if (dateFrom && isNaN(Date.parse(dateFrom))) {
            return sendError(res, 'dateFrom must be a valid ISO date string', null, 400);
        }

        if (dateTo && isNaN(Date.parse(dateTo))) {
            return sendError(res, 'dateTo must be a valid ISO date string', null, 400);
        }

        const { realmId } = req.qbAuth!;

        // Get sync logs through service
        const result = await syncLogService.getSyncLogs({
            page: pageNum,
            limit: limitNum,
            transactionType: transactionType || undefined,
            status: status || undefined,
            operation: operation || undefined,
            realmId,
            dateFrom: dateFrom || undefined,
            dateTo: dateTo || undefined,
            systemTransactionId: systemTransactionId || undefined,
            quickbooksId: quickbooksId || undefined
        });

        const responseData = {
            syncLogs: result.syncLogs,
            pagination: {
                currentPage: result.currentPage,
                totalPages: result.totalPages,
                totalCount: result.totalCount,
                hasNextPage: result.currentPage < result.totalPages,
                hasPreviousPage: result.currentPage > 1
            },
            filters: {
                transactionType: transactionType || null,
                status: status || null,
                operation: operation || null,
                dateFrom: dateFrom || null,
                dateTo: dateTo || null,
                systemTransactionId: systemTransactionId || null,
                quickbooksId: quickbooksId || null
            },
            summary: result.summary
        };

        return sendSuccess(res, 'Sync logs retrieved successfully', responseData);

    } catch (error) {
        console.error('Error getting sync logs:', error);
        const statusCode = getStatusCode(error as Error);
        return sendError(
            res,
            'Failed to get sync logs',
            { 
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            statusCode
        );
    }
};

/**
 * Get sync log by ID
 * GET /api/v1/qbo/sync-logs/:syncLogId
 */
const getSyncLogById = async (req: Request, res: Response): Promise<Response> => {
    try {
        const { syncLogId } = req.params;

        if (!syncLogId) {
            return sendError(res, 'Sync log ID is required', null, 400);
        }

        const { realmId } = req.qbAuth!;

        // Get sync log through service
        const syncLog = await syncLogService.getSyncLogById(syncLogId, realmId);

        return sendSuccess(res, 'Sync log retrieved successfully', { syncLog });

    } catch (error) {
        console.error('Error getting sync log by ID:', error);
        const statusCode = getStatusCode(error as Error);
        return sendError(
            res,
            'Failed to get sync log',
            { 
                error: error instanceof Error ? error.message : 'Unknown error',
                syncLogId: req.params?.syncLogId
            },
            statusCode
        );
    }
};

/**
 * Get sync logs by transaction ID
 * GET /api/v1/qbo/sync-logs/transaction/:transactionId
 */
const getSyncLogsByTransactionId = async (req: Request, res: Response): Promise<Response> => {
    try {
        const { transactionId } = req.params;

        if (!transactionId) {
            return sendError(res, 'Transaction ID is required', null, 400);
        }

        const { realmId } = req.qbAuth!;

        // Get sync logs through service
        const syncLogs = await syncLogService.getSyncLogsByTransactionId(transactionId, realmId);

        const responseData = {
            syncLogs,
            summary: {
                totalLogs: syncLogs.length,
                successCount: syncLogs.filter(log => log.status === 'SUCCESS').length,
                failedCount: syncLogs.filter(log => log.status === 'FAILED').length,
                pendingCount: syncLogs.filter(log => log.status === 'PENDING').length,
                lastAttempt: syncLogs.length > 0 ? syncLogs[0].timestamp : null
            }
        };

        return sendSuccess(res, 'Transaction sync logs retrieved successfully', responseData);

    } catch (error) {
        console.error('Error getting sync logs by transaction ID:', error);
        const statusCode = getStatusCode(error as Error);
        return sendError(
            res,
            'Failed to get transaction sync logs',
            { 
                error: error instanceof Error ? error.message : 'Unknown error',
                transactionId: req.params?.transactionId
            },
            statusCode
        );
    }
};

// Export controller functions
const syncLogController = {
    getSyncLogs,
    getSyncLogById,
    getSyncLogsByTransactionId
};

export { syncLogController };