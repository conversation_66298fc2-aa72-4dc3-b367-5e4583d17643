// services/syncLogService.ts

import { prisma } from '../config/db'; 
import { SyncLogsResult } from '../types/syncLogs';


const findConnection = async (realmId: string): Promise<string> => {
    try {
        const connection = await prisma.qBOConnection.findUnique({
            where: { realmId }
        });

        if (!connection) {
            throw new Error(`QuickBooks connection not found for realm: ${realmId}`);
        }

        return connection.id;
    } catch (error) {
        console.error('Error finding QBO connection:', error);
        throw new Error(`Failed to find QBO connection: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

/**
 * Get all sync logs with pagination and filtering
 */
const getSyncLogs = async (options: {
    page: number;
    limit: number;
    transactionType?: string;
    status?: string;
    operation?: string;
    realmId: string;
    dateFrom?: string;
    dateTo?: string;
    systemTransactionId?: string;
    quickbooksId?: string;
}): Promise<SyncLogsResult> => {
    try {
        const { 
            page, 
            limit, 
            transactionType, 
            status, 
            operation, 
            realmId, 
            dateFrom, 
            dateTo, 
            systemTransactionId,
            quickbooksId 
        } = options;
        const skip = (page - 1) * limit;

        // Get QBO connection for this realm
        const qboConnectionId = await findConnection(realmId);

        // Build where clause
        const where: any = {
            qboConnectionId
        };

        if (transactionType) {
            where.transactionType = transactionType;
        }

        if (status) {
            where.status = status;
        }

        if (operation) {
            where.operation = operation;
        }

        if (systemTransactionId) {
            where.systemTransactionId = systemTransactionId;
        }

        if (quickbooksId) {
            where.quickbooksId = quickbooksId;
        }

        if (dateFrom || dateTo) {
            where.timestamp = {};
            if (dateFrom) where.timestamp.gte = new Date(dateFrom);
            if (dateTo) where.timestamp.lte = new Date(dateTo);
        }

        // Get total count
        const totalCount = await prisma.syncLog.count({ where });

        // Get sync logs with pagination (excluding requestPayload and responsePayload)
        const syncLogs = await prisma.syncLog.findMany({
            where,
            select: {
                id: true,
                syncId: true,
                transactionType: true,
                systemTransactionId: true,
                quickbooksId: true,
                status: true,
                operation: true,
                qboConnectionId: true,
                invoiceId: true,
                paymentId: true,
                errorMessage: true,
                errorCode: true,
                timestamp: true,
                startedAt: true,
                completedAt: true,
                duration: true,
                retryCount: true,
                maxRetries: true,
                nextRetryAt: true,
                createdAt: true,
                updatedAt: true,
                // Exclude requestPayload and responsePayload
                invoice: {
                    select: {
                        docNumber: true,
                        total: true,
                        status: true
                    }
                },
                payment: {
                    select: {
                        referenceNumber: true,
                        amount: true,
                        status: true
                    }
                }
            },
            orderBy: { timestamp: 'desc' },
            skip,
            take: limit
        });

        const totalPages = Math.ceil(totalCount / limit);

        // Calculate summary statistics for this connection
        const allLogs = await prisma.syncLog.findMany({
            where: { qboConnectionId },
            select: {
                status: true,
                transactionType: true,
                duration: true
            }
        });

        const successCount = allLogs.filter(log => log.status === 'SUCCESS').length;
        const failedCount = allLogs.filter(log => log.status === 'FAILED').length;
        const pendingCount = allLogs.filter(log => log.status === 'PENDING').length;
        const inProgressCount = allLogs.filter(log => log.status === 'IN_PROGRESS').length;
        const invoiceLogs = allLogs.filter(log => log.transactionType === 'INVOICE').length;
        const paymentLogs = allLogs.filter(log => log.transactionType === 'PAYMENT').length;

        // Calculate average duration (excluding null values)
        const logsWithDuration = allLogs.filter(log => log.duration !== null);
        const averageDuration = logsWithDuration.length > 0 
            ? logsWithDuration.reduce((sum, log) => sum + (log.duration || 0), 0) / logsWithDuration.length
            : 0;

        const summary = {
            totalLogs: allLogs.length,
            successCount,
            failedCount,
            pendingCount,
            inProgressCount,
            invoiceLogs,
            paymentLogs,
            averageDuration: Math.round(averageDuration)
        };

        return {
            syncLogs,
            totalCount,
            totalPages,
            currentPage: page,
            summary
        };

    } catch (error) {
        throw new Error(`Failed to get sync logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

/**
 * Get sync log by ID
 */
const getSyncLogById = async (syncLogId: string, realmId: string): Promise<any> => {
    try {
        // Get QBO connection for this realm
        const qboConnectionId = await findConnection(realmId);

        const syncLog = await prisma.syncLog.findFirst({
            where: {
                id: syncLogId,
                qboConnectionId
            },
            select: {
                id: true,
                syncId: true,
                transactionType: true,
                systemTransactionId: true,
                quickbooksId: true,
                status: true,
                operation: true,
                qboConnectionId: true,
                invoiceId: true,
                paymentId: true,
                errorMessage: true,
                errorCode: true,
                timestamp: true,
                startedAt: true,
                completedAt: true,
                duration: true,
                retryCount: true,
                maxRetries: true,
                nextRetryAt: true,
                createdAt: true,
                updatedAt: true,
                // Exclude requestPayload and responsePayload
                invoice: {
                    select: {
                        docNumber: true,
                        total: true,
                        status: true,
                        customer: {
                            select: {
                                displayName: true,
                                email: true
                            }
                        }
                    }
                },
                payment: {
                    select: {
                        referenceNumber: true,
                        amount: true,
                        status: true,
                        paymentMethod: true
                    }
                }
            }
        });

        if (!syncLog) {
            throw new Error(`Sync log with ID ${syncLogId} not found`);
        }

        return syncLog;

    } catch (error) {
        throw new Error(`Failed to get sync log: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

/**
 * Get sync logs by transaction ID (invoice or payment)
 */
const getSyncLogsByTransactionId = async (transactionId: string, realmId: string): Promise<any[]> => {
    try {
        // Get QBO connection for this realm
        const qboConnectionId = await findConnection(realmId);

        const syncLogs = await prisma.syncLog.findMany({
            where: {
                systemTransactionId: transactionId,
                qboConnectionId
            },
            select: {
                id: true,
                syncId: true,
                transactionType: true,
                systemTransactionId: true,
                quickbooksId: true,
                status: true,
                operation: true,
                errorMessage: true,
                errorCode: true,
                timestamp: true,
                startedAt: true,
                completedAt: true,
                duration: true,
                retryCount: true,
                maxRetries: true,
                nextRetryAt: true
                // Exclude requestPayload and responsePayload
            },
            orderBy: { timestamp: 'desc' }
        });

        return syncLogs;

    } catch (error) {
        throw new Error(`Failed to get sync logs for transaction: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

// Export service functions
const syncLogService = {
    getSyncLogs,
    getSyncLogById,
    getSyncLogsByTransactionId
};

export default syncLogService;